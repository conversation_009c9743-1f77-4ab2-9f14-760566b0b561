# Fast Apply 功能详细分析

## 概述

Fast Apply 是 Void 编辑器中用于快速编辑大文件的核心功能。它通过让大模型输出搜索替换块（Search/Replace blocks）而不是重写整个文件来实现高效的代码编辑。

## 核心原理

### 两种 Apply 模式对比

1. **Fast Apply (搜索替换模式)**
   - 适用于大文件（≥1000字符）
   - 大模型输出搜索替换块
   - 只修改文件的特定部分
   - 性能优异

2. **Slow Apply (重写模式)**
   - 适用于小文件（<1000字符）
   - 大模型重写整个文件
   - 适合空文件或简单场景

### 触发条件

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:1142-1156
if (this._settingsService.state.globalSettings.enableFastApply) {
    const numCharsInFile = this._fileLengthOfGivenURI(opts.uri)
    if (numCharsInFile === null) return null
    if (numCharsInFile < 1000) { // slow apply for short files
        res = this._initializeWriteoverStream(opts)
    }
    else {
        res = this._initializeSearchAndReplaceStream(opts) // fast apply
    }
}
```

## 大模型指令设计

### 系统消息（System Message）

```typescript
// 来源：src/vs/workbench/contrib/void/common/prompt/prompts.ts:60-82
const createSearchReplaceBlocks_systemMessage = `\
You are a coding assistant that takes in a diff, and outputs SEARCH/REPLACE code blocks to implement the change(s) in the diff.
The diff will be labeled \`DIFF\` and the original file will be labeled \`ORIGINAL_FILE\`.

Format your SEARCH/REPLACE blocks as follows:
\`\`\`
<<<<<<< ORIGINAL
// ... original code goes here
=======
// ... final code goes here
>>>>>>> UPDATED

<<<<<<< ORIGINAL
// ... original code goes here
=======
// ... final code goes here
>>>>>>> UPDATED
\`\`\`

1. Your SEARCH/REPLACE block(s) must implement the diff EXACTLY. Do NOT leave anything out.
2. You are allowed to output multiple SEARCH/REPLACE blocks if needed.
3. Assume any comments in the diff are PART OF THE CHANGE. Include them in the output.
4. Your output should consist ONLY of SEARCH/REPLACE blocks. Do NOT output any text or explanations before or after this.
5. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace, comments, or modifications from the original code.
6. Each ORIGINAL text must be large enough to uniquely identify the change in the file. However, bias towards writing as little as possible.
7. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.
`
```

### 用户消息（User Message）

```typescript
// 来源：src/vs/workbench/contrib/void/common/prompt/prompts.ts:685-692
export const searchReplaceGivenDescription_userMessage = ({ originalCode, applyStr }: { originalCode: string, applyStr: string }) => `\
DIFF
${applyStr}

ORIGINAL_FILE
\`\`\`
${originalCode}
\`\`\``;
```

### 搜索替换块格式定义

```typescript
// 来源：src/vs/workbench/contrib/void/common/prompt/prompts.ts:38-40
export const ORIGINAL = `<<<<<<< ORIGINAL`
export const DIVIDER = `=======`
export const FINAL = `>>>>>>> UPDATED`
```

## 解析机制

### 搜索替换块解析器

```typescript
// 来源：src/vs/workbench/contrib/void/common/helpers/extractCodeFromResult.ts:187-246
export const extractSearchReplaceBlocks = (str: string) => {
    const ORIGINAL_ = ORIGINAL + `\n`
    const DIVIDER_ = '\n' + DIVIDER + `\n`

    const blocks: ExtractedSearchReplaceBlock[] = []

    let i = 0 // search i and beyond
    while (true) {
        let origStart = str.indexOf(ORIGINAL_, i)
        if (origStart === -1) { return blocks }
        origStart += ORIGINAL_.length
        i = origStart

        let dividerStart = str.indexOf(DIVIDER_, i)
        if (dividerStart === -1) { // still writing original
            const writingDIVIDERlen = endsWithAnyPrefixOf(str, DIVIDER_)?.length ?? 0
            blocks.push({
                orig: voidSubstr(str, origStart, str.length - writingDIVIDERlen),
                final: '',
                state: 'writingOriginal'
            })
            return blocks
        }

        const origStrDone = voidSubstr(str, origStart, dividerStart)
        dividerStart += DIVIDER_.length
        i = dividerStart

        const fullFINALStart = str.indexOf(FINAL, i)
        const fullFINALStart_ = str.indexOf('\n' + FINAL, i)
        const matchedFullFINAL_ = fullFINALStart_ !== -1 && fullFINALStart === fullFINALStart_ + 1

        let finalStart = matchedFullFINAL_ ? fullFINALStart_ : fullFINALStart
        if (finalStart === -1) { // still writing final
            const writingFINALlen = endsWithAnyPrefixOf(str, FINAL)?.length ?? 0
            const writingFINALlen_ = endsWithAnyPrefixOf(str, '\n' + FINAL)?.length ?? 0
            const usingWritingFINALlen = Math.max(writingFINALlen, writingFINALlen_)
            blocks.push({
                orig: origStrDone,
                final: voidSubstr(str, dividerStart, str.length - usingWritingFINALlen),
                state: 'writingFinal'
            })
            return blocks
        }

        const usingFINAL = matchedFullFINAL_ ? '\n' + FINAL : FINAL
        const finalStrDone = voidSubstr(str, dividerStart, finalStart)
        finalStart += usingFINAL.length
        i = finalStart

        blocks.push({
            orig: origStrDone,
            final: finalStrDone,
            state: 'done'
        })
    }
}
```

### 数据结构

```typescript
// 来源：src/vs/workbench/contrib/void/common/helpers/extractCodeFromResult.ts
export type ExtractedSearchReplaceBlock = {
    orig: string;      // 原始代码
    final: string;     // 替换后的代码
    state: 'writingOriginal' | 'writingFinal' | 'done';
}
```

## 文本查找机制

### findTextInCode 函数

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:113-148
const findTextInCode = (text: string, fileContents: string, canFallbackToRemoveWhitespace: boolean, opts: { startingAtLine?: number, returnType: 'lines' }) => {

    const returnAns = (fileContents: string, idx: number) => {
        const startLine = numLinesOfStr(fileContents.substring(0, idx + 1))
        const numLines = numLinesOfStr(text)
        const endLine = startLine + numLines - 1
        return [startLine, endLine] as const
    }

    const startingAtLineIdx = (fileContents: string) => opts?.startingAtLine !== undefined ?
        fileContents.split('\n').slice(0, opts.startingAtLine).join('\n').length
        : 0

    // 精确匹配
    let idx = fileContents.indexOf(text, startingAtLineIdx(fileContents))

    if (idx !== -1) {
        return returnAns(fileContents, idx)
    }

    if (!canFallbackToRemoveWhitespace)
        return 'Not found' as const

    // 回退：忽略空白字符匹配
    text = removeWhitespaceExceptNewlines(text)
    fileContents = removeWhitespaceExceptNewlines(fileContents)
    idx = fileContents.indexOf(text, startingAtLineIdx(fileContents));

    if (idx === -1) return 'Not found' as const
    const lastIdx = fileContents.lastIndexOf(text)
    if (lastIdx !== idx) return 'Not unique' as const

    return returnAns(fileContents, idx)
}
```

### 容错机制

1. **精确匹配优先**: 首先尝试精确匹配原始代码
2. **空白字符回退**: 如果精确匹配失败，移除空白字符后再次尝试
3. **唯一性检查**: 确保匹配结果在文件中是唯一的
4. **行号返回**: 返回1索引的行号范围

## 应用机制

### 核心应用函数

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:1616-1669
private _instantlyApplySRBlocks(uri: URI, blocksStr: string) {
    const blocks = extractSearchReplaceBlocks(blocksStr)
    if (blocks.length === 0) throw new Error(`No Search/Replace blocks were received!`)

    const { model } = this._voidModelService.getModel(uri)
    if (!model) throw new Error(`Error applying Search/Replace blocks: File does not exist.`)
    const modelStr = model.getValue(EndOfLinePreference.LF)
    const modelStrLines = modelStr.split('\n')

    // 1. 构建替换操作数组
    const replacements: { origStart: number; origEnd: number; block: ExtractedSearchReplaceBlock }[] = []
    for (const b of blocks) {
        const res = findTextInCode(b.orig, modelStr, true, { returnType: 'lines' })
        if (typeof res === 'string')
            throw new Error(this._errContentOfInvalidStr(res, b.orig))
        let [startLine, endLine] = res
        startLine -= 1 // 转为0索引
        endLine -= 1

        // 计算字符位置（包含换行符）
        const origStart = (startLine !== 0 ?
            modelStrLines.slice(0, startLine).join('\n') + '\n'
            : '').length

        const origEnd = modelStrLines.slice(0, endLine + 1).join('\n').length - 1

        replacements.push({ origStart, origEnd, block: b });
    }

    // 2. 按位置排序
    replacements.sort((a, b) => a.origStart - b.origStart)

    // 3. 检查重叠
    for (let i = 1; i < replacements.length; i++) {
        if (replacements[i].origStart <= replacements[i - 1].origEnd) {
            throw new Error(this._errContentOfInvalidStr('Has overlap', replacements[i]?.block?.orig))
        }
    }

    // 4. 从右到左应用替换（避免索引偏移）
    let newCode: string = modelStr
    for (let i = replacements.length - 1; i >= 0; i--) {
        const { origStart, origEnd, block } = replacements[i]
        newCode = newCode.slice(0, origStart) + block.final + newCode.slice(origEnd + 1, Infinity)
    }

    // 5. 写入文件
    this._writeURIText(uri, newCode, 'wholeFileRange', { shouldRealignDiffAreas: true })
}
```

### 关键步骤解析

1. **解析搜索替换块**: 从大模型输出中提取所有的搜索替换对
2. **定位原始代码**: 在文件中找到每个原始代码块的精确位置
3. **计算字符位置**: 将行号转换为字符索引位置
4. **排序和验证**: 按位置排序并检查是否有重叠
5. **逆序应用**: 从文件末尾开始应用替换，避免索引偏移问题
6. **写入文件**: 将修改后的内容写回文件

## 流式处理支持

### 实时解析和应用

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:1800-1850
const onText = (params: { fullText: string; fullReasoning: string }) => {
    const { fullText } = params
    // blocks are [done done done ... {writingFinal|writingOriginal}]
    //               ^
    //              currStreamingBlockNum

    const blocks = extractSearchReplaceBlocks(fullText)

    for (let blockNum = currStreamingBlockNum; blockNum < blocks.length; blockNum += 1) {
        const block = blocks[blockNum]

        // 如果是第一次看到这个块，添加为diffarea开始流式传输
        if (!(blockNum in addedTrackingZoneOfBlockNum)) {
            const originalBounds = findTextInCode(block.orig, originalFileCode, true, { returnType: 'lines' })

            // 检查错误和重叠
            if (typeof originalBounds === 'string' || hasOverlap) {
                const errorMessage = typeof originalBounds === 'string' ? originalBounds : 'Has overlap'
                // 处理错误...
                continue
            }

            let [startLine, endLine] = originalBounds
            startLine -= 1 // 转为0索引
            endLine -= 1

            // 创建DiffArea用于显示变更
            const adding: AddDiffAreaOpts = {
                uri,
                startLine,
                endLine,
                metadata: { originalBounds: [startLine + 1, endLine + 1] }
            }
            const trackingZone = this._addDiffArea(adding)
            addedTrackingZoneOfBlockNum.push(trackingZone)
        }

        // 更新流式状态
        if (block.state === 'done') {
            currStreamingBlockNum = blockNum + 1
        }
    }
}
```

### 最终处理

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:1965-1984
onFinalMessage: async (params) => {
    const { fullText } = params
    onText(params)

    const blocks = extractSearchReplaceBlocks(fullText)
    if (blocks.length === 0) {
        this._notificationService.info(`Void: We ran Fast Apply, but the LLM didn't output any changes.`)
    }

    // 恢复原始文件内容
    this._writeURIText(uri, originalFileCode, 'wholeFileRange', { shouldRealignDiffAreas: true })

    try {
        // 应用所有搜索替换块
        this._instantlyApplySRBlocks(uri, fullText)
        onDone()
        resMessageDonePromise()
    }
    catch (e) {
        onError(e)
    }
}
```

## 错误处理和重试机制

### 重试逻辑

```typescript
// 来源：src/vs/workbench/contrib/void/browser/editCodeService.ts:1777-1794
const runSearchReplace = async () => {
    let shouldSendAnotherMessage = true
    let nMessagesSent = 0
    let currStreamingBlockNum = 0
    let aborted = false
    let weAreAborting = false

    while (shouldSendAnotherMessage) {
        shouldSendAnotherMessage = false
        nMessagesSent += 1

        if (nMessagesSent >= N_RETRIES) {
            const e = {
                message: `Tried to Fast Apply ${N_RETRIES} times but failed. This may be related to model intelligence, or it may an edit that's too complex. Please retry or disable Fast Apply.`,
                fullError: null
            }
            onError(e)
            break
        }

        // 发送消息到大模型...
    }
}
```

### 常见错误类型

1. **"Not found"**: 原始代码在文件中找不到
2. **"Not unique"**: 原始代码在文件中不唯一
3. **"Has overlap"**: 多个搜索替换块之间有重叠
4. **"No Search/Replace blocks"**: 大模型没有输出任何搜索替换块

## 工具集成

### 作为工具调用

```typescript
// 来源：src/vs/workbench/contrib/void/browser/toolsService.ts:429-445
edit_file: async ({ uri, searchReplaceBlocks }) => {
    await voidModelService.initializeModel(uri)
    if (this.commandBarService.getStreamState(uri) === 'streaming') {
        throw new Error(`Another LLM is currently making changes to this file. Please stop streaming for now and ask the user to resume later.`)
    }
    await editCodeService.callBeforeApplyOrEdit(uri)
    editCodeService.instantlyApplySearchReplaceBlocks({ uri, searchReplaceBlocks })

    // 获取lint错误
    const lintErrorsPromise = Promise.resolve().then(async () => {
        await timeout(2000)
        const { lintErrors } = this._getLintErrors(uri)
        return { lintErrors }
    })

    return { result: lintErrorsPromise }
}
```

## 性能优势

### 与传统方法对比

1. **传统重写方式**:
   - 需要重新生成整个文件内容
   - 大模型需要处理完整文件上下文
   - 网络传输量大
   - 容易出现格式问题

2. **Fast Apply方式**:
   - 只生成需要修改的部分
   - 大模型专注于具体变更
   - 网络传输量小
   - 保持原有代码格式

### 适用场景

- ✅ 大文件编辑（>1000字符）
- ✅ 局部代码修改
- ✅ 多处独立修改
- ✅ 保持代码格式
- ❌ 全文重构
- ❌ 空文件或极小文件
- ❌ 复杂的结构性变更

## 实现要点总结

### 关键技术点

1. **精确的文本匹配算法**: 支持精确匹配和容错匹配
2. **流式解析**: 支持实时解析大模型输出
3. **位置计算**: 准确计算字符位置和行号转换
4. **冲突检测**: 确保替换操作之间无重叠
5. **逆序应用**: 避免索引偏移问题
6. **错误恢复**: 完善的错误处理和重试机制

### 架构设计

1. **分离关注点**: 解析、查找、应用分别处理
2. **状态管理**: 清晰的流式状态跟踪
3. **错误边界**: 每个步骤都有错误处理
4. **可扩展性**: 支持多种应用模式

### 移植建议

1. **保持核心算法**: 搜索替换块的解析和应用逻辑
2. **适配编辑器API**: 根据目标编辑器调整文件操作接口
3. **优化提示词**: 根据目标大模型调整指令格式
4. **测试覆盖**: 重点测试边界情况和错误处理

这个Fast Apply机制的核心价值在于将复杂的文件编辑操作转换为简单的搜索替换操作，既保证了效率又保持了准确性。对于需要处理大文件编辑的VSCode插件来说，这是一个非常值得借鉴的设计模式。
```
