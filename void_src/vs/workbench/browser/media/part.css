/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .part {
	box-sizing: border-box;
	overflow: hidden;
}

.monaco-workbench .part > .drop-block-overlay.visible {
	visibility: visible;
}

.monaco-workbench .part > .drop-block-overlay {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	visibility: hidden;
	opacity: 0;
	z-index: 12;
}

.monaco-workbench .part > .title,
.monaco-workbench .part > .header-or-footer {
	display: none; /* Parts have to opt in to show area */
}

.monaco-workbench .part > .title,
.monaco-workbench .part > .header-or-footer {
	height: 35px;
	display: flex;
	box-sizing: border-box;
	overflow: hidden;
}

.monaco-workbench .part > .title {
	padding-left: 8px;
	padding-right: 8px;
}

.monaco-workbench .part > .title > .title-label {
	line-height: 35px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.monaco-workbench .part > .title > .title-label {
	padding-left: 12px;
}

.monaco-workbench .part > .title > .title-label h2 {
	font-size: 11px;
	cursor: default;
	font-weight: normal;
	margin: 0;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.monaco-workbench .part > .title > .title-label a {
	text-decoration: none;
	font-size: 13px;
	cursor: default;
}

.monaco-workbench .part > .title > .title-actions {
	height: 35px;
	flex: 1;
	padding-left: 5px;
}

.monaco-workbench .part > .title > .title-actions .action-label {
	display: block;
	background-size: 16px;
	background-position: center center;
	background-repeat: no-repeat;
	padding: 2px; /* Make sure view actions and container actions align */
}

.monaco-workbench .part > .title > .title-actions .action-label .label {
	display: none;
}

.monaco-workbench .part > .content {
	font-size: 13px;
}

.monaco-workbench .part > .content > .monaco-progress-container,
.monaco-workbench .part.editor > .content .editor-group-container > .monaco-progress-container {
	position: absolute;
	left: 0;
	top: 33px; /* at the bottom of the 35px height title container */
	z-index: 5; /* on top of things */
}
